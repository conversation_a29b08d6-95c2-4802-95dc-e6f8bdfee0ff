🎯 مرحباً بك في برنامج إدارة المهام
=======================================

📋 تم إنشاء برنامج إدارة المهام بنجاح!

🚀 للتشغيل السريع:
------------------
1. انقر نقرة مزدوجة على ملف: تجربة.bat
   أو
2. افتح Command Prompt واكتب: python تجربة.py

📁 الملفات المتوفرة:
-------------------
✅ تجربة.py - النسخة السريعة للتجربة
✅ تجربة.bat - ملف تشغيل Windows
✅ main.py - البرنامج الكامل مع جميع المميزات
✅ start.py - نسخة مبسطة للتشغيل السريع
✅ database.py - وحدة قاعدة البيانات
✅ task_dialog.py - نافذة إضافة وتعديل المهام

🎮 كيفية الاستخدام:
------------------
➕ إضافة مهمة: انقر "إضافة مهمة" وأدخل التفاصيل
✏️ تعديل مهمة: انقر نقرة مزدوجة على المهمة
🗑️ حذف مهمة: اختر المهمة وانقر "حذف"
🔍 البحث: اكتب في مربع البحث
🔄 تحديث: انقر "تحديث" لإعادة تحميل المهام

💡 نصائح:
----------
- البرنامج يحفظ البيانات تلقائياً في قاعدة بيانات
- يمكنك إغلاق البرنامج وإعادة فتحه والبيانات ستبقى محفوظة
- البحث يعمل في العنوان والوصف
- يمكنك استخدام النقر المزدوج للتعديل السريع

🔧 إذا واجهت مشاكل:
-------------------
- تأكد من تثبيت Python على النظام
- تأكد من وجود صلاحيات الكتابة في المجلد
- جرب تشغيل: pip install tkcalendar

🎉 استمتع بالبرنامج!
==================
