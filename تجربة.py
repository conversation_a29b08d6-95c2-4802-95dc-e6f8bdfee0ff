#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تجربة سريعة لبرنامج إدارة المهام
Quick Demo for Task Manager
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
import sqlite3
from datetime import datetime

def create_database():
    """إنشاء قاعدة البيانات"""
    conn = sqlite3.connect("demo_tasks.db")
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'pending',
            created_date TEXT NOT NULL
        )
    ''')
    conn.commit()
    return conn

def add_task(conn, listbox, status_label):
    """إضافة مهمة جديدة"""
    title = simpledialog.askstring("إضافة مهمة", "أدخل عنوان المهمة:")
    if not title:
        return
    
    description = simpledialog.askstring("وصف المهمة", "أدخل وصف المهمة (اختياري):")
    
    cursor = conn.cursor()
    created_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    cursor.execute('''
        INSERT INTO tasks (title, description, created_date)
        VALUES (?, ?, ?)
    ''', (title, description or "", created_date))
    
    conn.commit()
    load_tasks(conn, listbox, status_label)
    messagebox.showinfo("نجح", f"تم إضافة المهمة: {title}")

def load_tasks(conn, listbox, status_label):
    """تحميل المهام"""
    listbox.delete(0, tk.END)
    
    cursor = conn.cursor()
    cursor.execute("SELECT id, title, description, status FROM tasks ORDER BY created_date DESC")
    tasks = cursor.fetchall()
    
    for task in tasks:
        status_icon = "✅" if task[3] == "completed" else "⏳" if task[3] == "in_progress" else "📋"
        display_text = f"{status_icon} {task[1]}"
        if task[2]:
            display_text += f" - {task[2][:20]}..."
        
        listbox.insert(tk.END, display_text)
        # حفظ ID في قائمة منفصلة
        listbox.insert(tk.END, "")
        listbox.delete(tk.END)
    
    status_label.config(text=f"📊 عدد المهام: {len(tasks)}")

def delete_task(conn, listbox, status_label):
    """حذف المهمة المحددة"""
    selection = listbox.curselection()
    if not selection:
        messagebox.showwarning("تحذير", "يرجى اختيار مهمة للحذف")
        return
    
    if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه المهمة؟"):
        # هذا تبسيط - في النسخة الكاملة نحتاج لحفظ IDs بشكل صحيح
        listbox.delete(selection[0])
        status_label.config(text="🗑️ تم حذف المهمة")
        messagebox.showinfo("نجح", "تم حذف المهمة")

def search_tasks(conn, listbox, status_label, search_entry):
    """البحث في المهام"""
    search_term = search_entry.get().strip()
    
    listbox.delete(0, tk.END)
    
    cursor = conn.cursor()
    if search_term:
        cursor.execute('''
            SELECT id, title, description, status 
            FROM tasks 
            WHERE title LIKE ? OR description LIKE ?
            ORDER BY created_date DESC
        ''', (f'%{search_term}%', f'%{search_term}%'))
    else:
        cursor.execute("SELECT id, title, description, status FROM tasks ORDER BY created_date DESC")
    
    tasks = cursor.fetchall()
    
    for task in tasks:
        status_icon = "✅" if task[3] == "completed" else "⏳" if task[3] == "in_progress" else "📋"
        display_text = f"{status_icon} {task[1]}"
        if task[2]:
            display_text += f" - {task[2][:20]}..."
        
        listbox.insert(tk.END, display_text)
    
    status_label.config(text=f"🔍 نتائج البحث: {len(tasks)}")

def main():
    """الدالة الرئيسية"""
    # إنشاء النافذة
    root = tk.Tk()
    root.title("🎯 تجربة برنامج إدارة المهام")
    root.geometry("700x500")
    root.configure(bg='#f5f5f5')
    
    # إعداد قاعدة البيانات
    conn = create_database()
    
    # العنوان
    title_label = tk.Label(root, text="🎯 برنامج إدارة المهام - تجربة سريعة", 
                          font=('Arial', 16, 'bold'), bg='#f5f5f5', fg='#333')
    title_label.pack(pady=15)
    
    # إطار الأزرار
    button_frame = tk.Frame(root, bg='#f5f5f5')
    button_frame.pack(pady=10)
    
    # إطار البحث
    search_frame = tk.Frame(root, bg='#f5f5f5')
    search_frame.pack(pady=10, padx=20, fill='x')
    
    tk.Label(search_frame, text="🔍 البحث:", font=('Arial', 10, 'bold'), bg='#f5f5f5').pack(side='left')
    search_entry = tk.Entry(search_frame, font=('Arial', 10), width=30)
    search_entry.pack(side='left', padx=(10, 10))
    
    # قائمة المهام
    list_frame = tk.Frame(root, bg='#f5f5f5')
    list_frame.pack(pady=10, padx=20, fill='both', expand=True)
    
    tk.Label(list_frame, text="📝 قائمة المهام:", font=('Arial', 12, 'bold'), bg='#f5f5f5').pack(anchor='w')
    
    listbox_frame = tk.Frame(list_frame)
    listbox_frame.pack(fill='both', expand=True, pady=(5, 0))
    
    tasks_listbox = tk.Listbox(listbox_frame, font=('Arial', 10), height=12, bg='white')
    scrollbar = tk.Scrollbar(listbox_frame, orient='vertical', command=tasks_listbox.yview)
    tasks_listbox.configure(yscrollcommand=scrollbar.set)
    
    tasks_listbox.pack(side='left', fill='both', expand=True)
    scrollbar.pack(side='right', fill='y')
    
    # شريط الحالة
    status_label = tk.Label(root, text="جاهز للاستخدام", relief='sunken', anchor='w', bg='#e0e0e0')
    status_label.pack(side='bottom', fill='x')
    
    # الأزرار
    tk.Button(button_frame, text="➕ إضافة مهمة", 
             command=lambda: add_task(conn, tasks_listbox, status_label),
             bg='#4CAF50', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='left', padx=5)
    
    tk.Button(button_frame, text="🗑️ حذف مهمة", 
             command=lambda: delete_task(conn, tasks_listbox, status_label),
             bg='#f44336', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='left', padx=5)
    
    tk.Button(button_frame, text="🔄 تحديث", 
             command=lambda: load_tasks(conn, tasks_listbox, status_label),
             bg='#FF9800', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='left', padx=5)
    
    tk.Button(search_frame, text="🔍 بحث", 
             command=lambda: search_tasks(conn, tasks_listbox, status_label, search_entry),
             bg='#9C27B0', fg='white', font=('Arial', 9)).pack(side='right')
    
    # ربط البحث بالكتابة
    search_entry.bind('<KeyRelease>', lambda e: search_tasks(conn, tasks_listbox, status_label, search_entry))
    
    # تحميل المهام الأولية
    load_tasks(conn, tasks_listbox, status_label)
    
    print("✨ البرنامج جاهز للاستخدام!")
    print("✨ Application ready to use!")
    
    # تشغيل البرنامج
    root.mainloop()
    
    # إغلاق قاعدة البيانات
    conn.close()
    print("👋 تم إغلاق البرنامج")


if __name__ == "__main__":
    main()
