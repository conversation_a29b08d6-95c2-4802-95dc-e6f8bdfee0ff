# -*- coding: utf-8 -*-
"""
برنامج إدارة المهام - الواجهة الرئيسية
Task Management System - Main Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
import os
import sys

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import TaskDatabase
from task_dialog import TaskDialog

class TaskManagerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج إدارة المهام - Task Manager")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # تهيئة قاعدة البيانات
        self.db = TaskDatabase()
        
        # إعداد الخطوط العربية
        self.setup_fonts()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحديث قائمة المهام
        self.refresh_tasks()
    
    def setup_fonts(self):
        """
        إعداد الخطوط العربية
        Setup Arabic fonts
        """
        try:
            # محاولة استخدام خط عربي
            self.arabic_font = ('Arial Unicode MS', 10)
            self.arabic_font_bold = ('Arial Unicode MS', 10, 'bold')
            self.arabic_font_large = ('Arial Unicode MS', 12, 'bold')
        except:
            # استخدام الخط الافتراضي إذا لم يتوفر الخط العربي
            self.arabic_font = ('Arial', 10)
            self.arabic_font_bold = ('Arial', 10, 'bold')
            self.arabic_font_large = ('Arial', 12, 'bold')
    
    def create_widgets(self):
        """
        إنشاء عناصر الواجهة
        Create interface widgets
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="برنامج إدارة المهام", 
                               font=self.arabic_font_large)
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # إطار البحث والأزرار
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(1, weight=1)
        
        # مربع البحث
        ttk.Label(control_frame, text="البحث:", font=self.arabic_font).grid(row=0, column=0, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(control_frame, textvariable=self.search_var, font=self.arabic_font)
        self.search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.on_search)
        
        # أزرار التحكم
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=2)
        
        ttk.Button(button_frame, text="إضافة مهمة", command=self.add_task,
                  style='Accent.TButton').grid(row=0, column=0, padx=2)
        ttk.Button(button_frame, text="تعديل", command=self.edit_task).grid(row=0, column=1, padx=2)
        ttk.Button(button_frame, text="حذف", command=self.delete_task).grid(row=0, column=2, padx=2)
        ttk.Button(button_frame, text="تحديث", command=self.refresh_tasks).grid(row=0, column=3, padx=2)
        
        # إطار الفلترة
        filter_frame = ttk.Frame(main_frame)
        filter_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(filter_frame, text="فلترة حسب:", font=self.arabic_font).grid(row=0, column=0, padx=(0, 5))
        
        # فلترة حسب الحالة
        ttk.Label(filter_frame, text="الحالة:", font=self.arabic_font).grid(row=0, column=1, padx=(10, 5))
        self.status_filter = ttk.Combobox(filter_frame, values=["الكل", "معلقة", "قيد التنفيذ", "مكتملة"], 
                                         state="readonly", width=10)
        self.status_filter.set("الكل")
        self.status_filter.grid(row=0, column=2, padx=(0, 10))
        self.status_filter.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # فلترة حسب الأولوية
        ttk.Label(filter_frame, text="الأولوية:", font=self.arabic_font).grid(row=0, column=3, padx=(10, 5))
        self.priority_filter = ttk.Combobox(filter_frame, values=["الكل", "عالية", "متوسطة", "منخفضة"], 
                                           state="readonly", width=10)
        self.priority_filter.set("الكل")
        self.priority_filter.grid(row=0, column=4, padx=(0, 10))
        self.priority_filter.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # جدول المهام
        self.create_task_table(main_frame)
        
        # شريط الحالة
        self.status_bar = ttk.Label(main_frame, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def create_task_table(self, parent):
        """
        إنشاء جدول المهام
        Create tasks table
        """
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # إنشاء Treeview
        columns = ('ID', 'العنوان', 'الوصف', 'الحالة', 'الأولوية', 'تاريخ الإنشاء', 'تاريخ الاستحقاق')
        self.task_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        self.task_tree.heading('ID', text='المعرف')
        self.task_tree.heading('العنوان', text='العنوان')
        self.task_tree.heading('الوصف', text='الوصف')
        self.task_tree.heading('الحالة', text='الحالة')
        self.task_tree.heading('الأولوية', text='الأولوية')
        self.task_tree.heading('تاريخ الإنشاء', text='تاريخ الإنشاء')
        self.task_tree.heading('تاريخ الاستحقاق', text='تاريخ الاستحقاق')
        
        # تعريف عرض الأعمدة
        self.task_tree.column('ID', width=50, minwidth=50)
        self.task_tree.column('العنوان', width=200, minwidth=150)
        self.task_tree.column('الوصف', width=250, minwidth=200)
        self.task_tree.column('الحالة', width=100, minwidth=80)
        self.task_tree.column('الأولوية', width=100, minwidth=80)
        self.task_tree.column('تاريخ الإنشاء', width=150, minwidth=120)
        self.task_tree.column('تاريخ الاستحقاق', width=150, minwidth=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول وشريط التمرير
        self.task_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # ربط الأحداث
        self.task_tree.bind('<Double-1>', self.on_task_double_click)
        self.task_tree.bind('<Button-3>', self.show_context_menu)  # النقر بالزر الأيمن
    
    def refresh_tasks(self):
        """
        تحديث قائمة المهام
        Refresh tasks list
        """
        # مسح الجدول
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        
        # الحصول على المهام من قاعدة البيانات
        tasks = self.db.get_all_tasks()
        
        # إضافة المهام للجدول
        for task in tasks:
            # تحويل الحالة والأولوية للعربية
            status_map = {'pending': 'معلقة', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتملة'}
            priority_map = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}
            
            status = status_map.get(task[3], task[3])
            priority = priority_map.get(task[4], task[4])
            
            # تنسيق التواريخ
            created_date = task[5][:16] if task[5] else ""
            due_date = task[6][:16] if task[6] else ""
            
            self.task_tree.insert('', 'end', values=(
                task[0],  # ID
                task[1],  # العنوان
                task[2][:50] + "..." if task[2] and len(task[2]) > 50 else task[2] or "",  # الوصف
                status,   # الحالة
                priority, # الأولوية
                created_date,  # تاريخ الإنشاء
                due_date  # تاريخ الاستحقاق
            ))
        
        # تحديث شريط الحالة
        self.status_bar.config(text=f"إجمالي المهام: {len(tasks)}")
    
    def on_search(self, event=None):
        """
        البحث في المهام
        Search in tasks
        """
        search_term = self.search_var.get().strip()
        
        # مسح الجدول
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        
        # البحث في قاعدة البيانات
        if search_term:
            tasks = self.db.search_tasks(search_term)
        else:
            tasks = self.db.get_all_tasks()
        
        # إضافة النتائج للجدول
        for task in tasks:
            status_map = {'pending': 'معلقة', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتملة'}
            priority_map = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}
            
            status = status_map.get(task[3], task[3])
            priority = priority_map.get(task[4], task[4])
            
            created_date = task[5][:16] if task[5] else ""
            due_date = task[6][:16] if task[6] else ""
            
            self.task_tree.insert('', 'end', values=(
                task[0], task[1], 
                task[2][:50] + "..." if task[2] and len(task[2]) > 50 else task[2] or "",
                status, priority, created_date, due_date
            ))
        
        # تحديث شريط الحالة
        self.status_bar.config(text=f"نتائج البحث: {len(tasks)}")
    
    def apply_filters(self, event=None):
        """
        تطبيق الفلاتر
        Apply filters
        """
        # مسح الجدول
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        
        # الحصول على قيم الفلاتر
        status_filter = self.status_filter.get()
        priority_filter = self.priority_filter.get()
        
        # تحويل القيم للإنجليزية
        status_map_reverse = {'معلقة': 'pending', 'قيد التنفيذ': 'in_progress', 'مكتملة': 'completed'}
        priority_map_reverse = {'عالية': 'high', 'متوسطة': 'medium', 'منخفضة': 'low'}
        
        # الحصول على المهام
        tasks = self.db.get_all_tasks()
        
        # تطبيق الفلاتر
        filtered_tasks = []
        for task in tasks:
            # فلترة حسب الحالة
            if status_filter != "الكل":
                if task[3] != status_map_reverse.get(status_filter):
                    continue
            
            # فلترة حسب الأولوية
            if priority_filter != "الكل":
                if task[4] != priority_map_reverse.get(priority_filter):
                    continue
            
            filtered_tasks.append(task)
        
        # إضافة المهام المفلترة للجدول
        for task in filtered_tasks:
            status_map = {'pending': 'معلقة', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتملة'}
            priority_map = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}
            
            status = status_map.get(task[3], task[3])
            priority = priority_map.get(task[4], task[4])
            
            created_date = task[5][:16] if task[5] else ""
            due_date = task[6][:16] if task[6] else ""
            
            self.task_tree.insert('', 'end', values=(
                task[0], task[1],
                task[2][:50] + "..." if task[2] and len(task[2]) > 50 else task[2] or "",
                status, priority, created_date, due_date
            ))
        
        # تحديث شريط الحالة
        self.status_bar.config(text=f"المهام المفلترة: {len(filtered_tasks)}")

    def add_task(self):
        """
        إضافة مهمة جديدة
        Add new task
        """
        dialog = TaskDialog(self.root, "إضافة مهمة جديدة")
        if dialog.result:
            task_data = dialog.result
            try:
                task_id = self.db.add_task(
                    title=task_data['title'],
                    description=task_data['description'],
                    priority=task_data['priority'],
                    due_date=task_data['due_date']
                )
                self.refresh_tasks()
                self.status_bar.config(text=f"تم إضافة المهمة بنجاح - المعرف: {task_id}")
                messagebox.showinfo("نجح", "تم إضافة المهمة بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المهمة: {str(e)}")

    def edit_task(self):
        """
        تعديل المهمة المحددة
        Edit selected task
        """
        selected_item = self.task_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للتعديل")
            return

        # الحصول على بيانات المهمة
        task_values = self.task_tree.item(selected_item[0])['values']
        task_id = task_values[0]

        # الحصول على بيانات المهمة من قاعدة البيانات
        task_data = self.db.get_task_by_id(task_id)
        if not task_data:
            messagebox.showerror("خطأ", "لم يتم العثور على المهمة")
            return

        # فتح نافذة التعديل
        dialog = TaskDialog(self.root, "تعديل المهمة", task_data)
        if dialog.result:
            new_data = dialog.result
            try:
                self.db.update_task(
                    task_id=task_id,
                    title=new_data['title'],
                    description=new_data['description'],
                    status=new_data['status'],
                    priority=new_data['priority'],
                    due_date=new_data['due_date']
                )
                self.refresh_tasks()
                self.status_bar.config(text=f"تم تحديث المهمة بنجاح - المعرف: {task_id}")
                messagebox.showinfo("نجح", "تم تحديث المهمة بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث المهمة: {str(e)}")

    def delete_task(self):
        """
        حذف المهمة المحددة
        Delete selected task
        """
        selected_item = self.task_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للحذف")
            return

        # الحصول على بيانات المهمة
        task_values = self.task_tree.item(selected_item[0])['values']
        task_id = task_values[0]
        task_title = task_values[1]

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المهمة:\n'{task_title}'؟"):
            try:
                self.db.delete_task(task_id)
                self.refresh_tasks()
                self.status_bar.config(text=f"تم حذف المهمة بنجاح - المعرف: {task_id}")
                messagebox.showinfo("نجح", "تم حذف المهمة بنجاح!")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المهمة: {str(e)}")

    def on_task_double_click(self, event):
        """
        النقر المزدوج على المهمة
        Double click on task
        """
        self.edit_task()

    def show_context_menu(self, event):
        """
        إظهار القائمة السياقية
        Show context menu
        """
        # تحديد العنصر المنقور عليه
        item = self.task_tree.identify_row(event.y)
        if item:
            self.task_tree.selection_set(item)

            # إنشاء القائمة السياقية
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="تعديل", command=self.edit_task)
            context_menu.add_command(label="حذف", command=self.delete_task)
            context_menu.add_separator()
            context_menu.add_command(label="تحديد كمكتملة", command=self.mark_completed)
            context_menu.add_command(label="تحديد كقيد التنفيذ", command=self.mark_in_progress)

            # إظهار القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def mark_completed(self):
        """
        تحديد المهمة كمكتملة
        Mark task as completed
        """
        selected_item = self.task_tree.selection()
        if selected_item:
            task_id = self.task_tree.item(selected_item[0])['values'][0]
            self.db.update_task(task_id, status='completed')
            self.refresh_tasks()
            self.status_bar.config(text=f"تم تحديد المهمة كمكتملة - المعرف: {task_id}")

    def mark_in_progress(self):
        """
        تحديد المهمة كقيد التنفيذ
        Mark task as in progress
        """
        selected_item = self.task_tree.selection()
        if selected_item:
            task_id = self.task_tree.item(selected_item[0])['values'][0]
            self.db.update_task(task_id, status='in_progress')
            self.refresh_tasks()
            self.status_bar.config(text=f"تم تحديد المهمة كقيد التنفيذ - المعرف: {task_id}")


def main():
    """
    الدالة الرئيسية لتشغيل البرنامج
    Main function to run the application
    """
    # إنشاء النافذة الرئيسية
    root = tk.Tk()

    # تطبيق ثيم حديث
    style = ttk.Style()
    try:
        style.theme_use('clam')  # ثيم حديث
    except:
        pass

    # إنشاء التطبيق
    app = TaskManagerApp(root)

    # تشغيل التطبيق
    root.mainloop()


if __name__ == "__main__":
    main()
