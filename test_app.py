# -*- coding: utf-8 -*-
"""
اختبار برنامج إدارة المهام
Test Task Manager Application
"""

import unittest
import os
import sys
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import TaskDatabase

class TestTaskDatabase(unittest.TestCase):
    def setUp(self):
        """
        إعداد الاختبار
        Test setup
        """
        # استخدام قاعدة بيانات اختبار
        self.test_db_name = "test_tasks.db"
        self.db = TaskDatabase(self.test_db_name)
    
    def tearDown(self):
        """
        تنظيف بعد الاختبار
        Test cleanup
        """
        # حذف قاعدة بيانات الاختبار
        if os.path.exists(self.test_db_name):
            os.remove(self.test_db_name)
    
    def test_add_task(self):
        """
        اختبار إضافة مهمة
        Test adding a task
        """
        task_id = self.db.add_task(
            title="مهمة اختبار",
            description="وصف مهمة الاختبار",
            priority="high",
            due_date="2024-12-31"
        )
        
        self.assertIsNotNone(task_id)
        self.assertGreater(task_id, 0)
        
        # التحقق من إضافة المهمة
        task = self.db.get_task_by_id(task_id)
        self.assertIsNotNone(task)
        self.assertEqual(task[1], "مهمة اختبار")
        self.assertEqual(task[2], "وصف مهمة الاختبار")
        self.assertEqual(task[4], "high")
    
    def test_get_all_tasks(self):
        """
        اختبار الحصول على جميع المهام
        Test getting all tasks
        """
        # إضافة مهام للاختبار
        self.db.add_task("مهمة 1", "وصف 1")
        self.db.add_task("مهمة 2", "وصف 2")
        self.db.add_task("مهمة 3", "وصف 3")
        
        tasks = self.db.get_all_tasks()
        self.assertEqual(len(tasks), 3)
    
    def test_search_tasks(self):
        """
        اختبار البحث في المهام
        Test searching tasks
        """
        # إضافة مهام للاختبار
        self.db.add_task("مهمة برمجة", "تطوير تطبيق ويب")
        self.db.add_task("مهمة تصميم", "تصميم واجهة المستخدم")
        self.db.add_task("مهمة اختبار", "اختبار التطبيق")
        
        # البحث عن "برمجة"
        results = self.db.search_tasks("برمجة")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0][1], "مهمة برمجة")
        
        # البحث عن "تطبيق"
        results = self.db.search_tasks("تطبيق")
        self.assertEqual(len(results), 2)  # يجب أن يجد مهمتين
    
    def test_update_task(self):
        """
        اختبار تحديث مهمة
        Test updating a task
        """
        # إضافة مهمة
        task_id = self.db.add_task("مهمة أصلية", "وصف أصلي")
        
        # تحديث المهمة
        self.db.update_task(
            task_id=task_id,
            title="مهمة محدثة",
            description="وصف محدث",
            status="completed",
            priority="low"
        )
        
        # التحقق من التحديث
        updated_task = self.db.get_task_by_id(task_id)
        self.assertEqual(updated_task[1], "مهمة محدثة")
        self.assertEqual(updated_task[2], "وصف محدث")
        self.assertEqual(updated_task[3], "completed")
        self.assertEqual(updated_task[4], "low")
        self.assertIsNotNone(updated_task[7])  # completed_date
    
    def test_delete_task(self):
        """
        اختبار حذف مهمة
        Test deleting a task
        """
        # إضافة مهمة
        task_id = self.db.add_task("مهمة للحذف", "سيتم حذفها")
        
        # التحقق من وجود المهمة
        task = self.db.get_task_by_id(task_id)
        self.assertIsNotNone(task)
        
        # حذف المهمة
        self.db.delete_task(task_id)
        
        # التحقق من حذف المهمة
        deleted_task = self.db.get_task_by_id(task_id)
        self.assertIsNone(deleted_task)
    
    def test_get_tasks_by_status(self):
        """
        اختبار الحصول على المهام حسب الحالة
        Test getting tasks by status
        """
        # إضافة مهام بحالات مختلفة
        task1_id = self.db.add_task("مهمة معلقة", "وصف")
        task2_id = self.db.add_task("مهمة قيد التنفيذ", "وصف")
        task3_id = self.db.add_task("مهمة مكتملة", "وصف")
        
        # تحديث حالات المهام
        self.db.update_task(task2_id, status="in_progress")
        self.db.update_task(task3_id, status="completed")
        
        # اختبار الحصول على المهام حسب الحالة
        pending_tasks = self.db.get_tasks_by_status("pending")
        in_progress_tasks = self.db.get_tasks_by_status("in_progress")
        completed_tasks = self.db.get_tasks_by_status("completed")
        
        self.assertEqual(len(pending_tasks), 1)
        self.assertEqual(len(in_progress_tasks), 1)
        self.assertEqual(len(completed_tasks), 1)
    
    def test_get_tasks_by_priority(self):
        """
        اختبار الحصول على المهام حسب الأولوية
        Test getting tasks by priority
        """
        # إضافة مهام بأولويات مختلفة
        self.db.add_task("مهمة عالية", "وصف", priority="high")
        self.db.add_task("مهمة متوسطة", "وصف", priority="medium")
        self.db.add_task("مهمة منخفضة", "وصف", priority="low")
        
        # اختبار الحصول على المهام حسب الأولوية
        high_tasks = self.db.get_tasks_by_priority("high")
        medium_tasks = self.db.get_tasks_by_priority("medium")
        low_tasks = self.db.get_tasks_by_priority("low")
        
        self.assertEqual(len(high_tasks), 1)
        self.assertEqual(len(medium_tasks), 1)
        self.assertEqual(len(low_tasks), 1)


def run_tests():
    """
    تشغيل جميع الاختبارات
    Run all tests
    """
    print("🧪 بدء تشغيل الاختبارات...")
    print("🧪 Starting tests...")
    print("-" * 50)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2, exit=False)
    
    print("-" * 50)
    print("✅ انتهت الاختبارات")
    print("✅ Tests completed")


if __name__ == "__main__":
    run_tests()
