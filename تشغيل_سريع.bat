@echo off
chcp 65001 >nul
title برنامج إدارة المهام - Task Manager

echo.
echo ========================================
echo    🚀 برنامج إدارة المهام
echo    🚀 Task Manager
echo ========================================
echo.

echo 📋 جاري تشغيل البرنامج...
echo 📋 Starting application...
echo.

python start.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    echo ❌ Error running the application
    echo.
    echo 💡 تأكد من تثبيت Python على النظام
    echo 💡 Make sure Python is installed
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
    echo ✅ Application closed successfully
)

pause
