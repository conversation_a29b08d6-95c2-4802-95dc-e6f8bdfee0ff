# -*- coding: utf-8 -*-
"""
نافذة حوار إضافة وتعديل المهام
Task Dialog for adding and editing tasks
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from tkcalendar import DateEntry

class TaskDialog:
    def __init__(self, parent, title, task_data=None):
        """
        تهيئة نافذة الحوار
        Initialize dialog window
        """
        self.result = None
        self.task_data = task_data
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الخطوط
        self.arabic_font = ('Arial Unicode MS', 10)
        self.arabic_font_bold = ('Arial Unicode MS', 10, 'bold')
        
        # إنشاء العناصر
        self.create_widgets()
        
        # تعبئة البيانات إذا كانت متوفرة (للتعديل)
        if task_data:
            self.populate_data()
        
        # التركيز على حقل العنوان
        self.title_entry.focus()
    
    def center_window(self):
        """
        توسيط النافذة على الشاشة
        Center window on screen
        """
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """
        إنشاء عناصر النافذة
        Create window widgets
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # عنوان المهمة
        ttk.Label(main_frame, text="عنوان المهمة *:", font=self.arabic_font_bold).grid(
            row=row, column=0, sticky=tk.W, pady=(0, 5))
        self.title_entry = ttk.Entry(main_frame, font=self.arabic_font, width=40)
        self.title_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        row += 1
        
        # وصف المهمة
        ttk.Label(main_frame, text="وصف المهمة:", font=self.arabic_font_bold).grid(
            row=row, column=0, sticky=(tk.W, tk.N), pady=(10, 5))
        
        # إطار النص مع شريط التمرير
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=row, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 5))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.description_text = tk.Text(text_frame, height=6, width=40, font=self.arabic_font, wrap=tk.WORD)
        scrollbar_desc = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=scrollbar_desc.set)
        
        self.description_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_desc.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        row += 1
        
        # الحالة (فقط للتعديل)
        if self.task_data:
            ttk.Label(main_frame, text="الحالة:", font=self.arabic_font_bold).grid(
                row=row, column=0, sticky=tk.W, pady=(10, 5))
            self.status_combo = ttk.Combobox(main_frame, values=["معلقة", "قيد التنفيذ", "مكتملة"], 
                                           state="readonly", font=self.arabic_font)
            self.status_combo.grid(row=row, column=1, sticky=tk.W, pady=(10, 5))
            row += 1
        
        # الأولوية
        ttk.Label(main_frame, text="الأولوية:", font=self.arabic_font_bold).grid(
            row=row, column=0, sticky=tk.W, pady=(10, 5))
        self.priority_combo = ttk.Combobox(main_frame, values=["عالية", "متوسطة", "منخفضة"], 
                                         state="readonly", font=self.arabic_font)
        self.priority_combo.set("متوسطة")
        self.priority_combo.grid(row=row, column=1, sticky=tk.W, pady=(10, 5))
        
        row += 1
        
        # تاريخ الاستحقاق
        ttk.Label(main_frame, text="تاريخ الاستحقاق:", font=self.arabic_font_bold).grid(
            row=row, column=0, sticky=tk.W, pady=(10, 5))
        
        date_frame = ttk.Frame(main_frame)
        date_frame.grid(row=row, column=1, sticky=tk.W, pady=(10, 5))
        
        try:
            self.due_date_entry = DateEntry(date_frame, width=12, background='darkblue',
                                          foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
            self.due_date_entry.grid(row=0, column=0, padx=(0, 10))
        except:
            # إذا لم تكن مكتبة tkcalendar متوفرة، استخدم Entry عادي
            self.due_date_entry = ttk.Entry(date_frame, width=15, font=self.arabic_font)
            self.due_date_entry.insert(0, "YYYY-MM-DD")
            self.due_date_entry.grid(row=0, column=0, padx=(0, 10))
        
        # زر مسح التاريخ
        ttk.Button(date_frame, text="مسح", command=self.clear_date).grid(row=0, column=1)
        
        row += 1
        
        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(button_frame, text="حفظ", command=self.save_task, 
                  style='Accent.TButton').grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="إلغاء", command=self.cancel).grid(row=0, column=1)
    
    def populate_data(self):
        """
        تعبئة البيانات للتعديل
        Populate data for editing
        """
        if not self.task_data:
            return
        
        # تعبئة العنوان
        self.title_entry.insert(0, self.task_data[1] or "")
        
        # تعبئة الوصف
        self.description_text.insert('1.0', self.task_data[2] or "")
        
        # تعبئة الحالة
        if hasattr(self, 'status_combo'):
            status_map = {'pending': 'معلقة', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتملة'}
            status = status_map.get(self.task_data[3], 'معلقة')
            self.status_combo.set(status)
        
        # تعبئة الأولوية
        priority_map = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}
        priority = priority_map.get(self.task_data[4], 'متوسطة')
        self.priority_combo.set(priority)
        
        # تعبئة تاريخ الاستحقاق
        if self.task_data[6]:  # due_date
            try:
                if hasattr(self.due_date_entry, 'set_date'):
                    # إذا كان DateEntry
                    date_obj = datetime.strptime(self.task_data[6][:10], '%Y-%m-%d').date()
                    self.due_date_entry.set_date(date_obj)
                else:
                    # إذا كان Entry عادي
                    self.due_date_entry.delete(0, tk.END)
                    self.due_date_entry.insert(0, self.task_data[6][:10])
            except:
                pass
    
    def clear_date(self):
        """
        مسح التاريخ
        Clear date
        """
        if hasattr(self.due_date_entry, 'set_date'):
            self.due_date_entry.set_date(None)
        else:
            self.due_date_entry.delete(0, tk.END)
    
    def save_task(self):
        """
        حفظ المهمة
        Save task
        """
        # التحقق من صحة البيانات
        title = self.title_entry.get().strip()
        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان المهمة")
            return
        
        # جمع البيانات
        description = self.description_text.get('1.0', tk.END).strip()
        
        # تحويل الحالة للإنجليزية
        status = 'pending'  # افتراضي للمهام الجديدة
        if hasattr(self, 'status_combo'):
            status_map_reverse = {'معلقة': 'pending', 'قيد التنفيذ': 'in_progress', 'مكتملة': 'completed'}
            status = status_map_reverse.get(self.status_combo.get(), 'pending')
        
        # تحويل الأولوية للإنجليزية
        priority_map_reverse = {'عالية': 'high', 'متوسطة': 'medium', 'منخفضة': 'low'}
        priority = priority_map_reverse.get(self.priority_combo.get(), 'medium')
        
        # الحصول على تاريخ الاستحقاق
        due_date = None
        try:
            if hasattr(self.due_date_entry, 'get_date'):
                date_obj = self.due_date_entry.get_date()
                if date_obj:
                    due_date = date_obj.strftime('%Y-%m-%d')
            else:
                date_text = self.due_date_entry.get().strip()
                if date_text and date_text != "YYYY-MM-DD":
                    # التحقق من صحة التاريخ
                    datetime.strptime(date_text, '%Y-%m-%d')
                    due_date = date_text
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
            return
        
        # حفظ النتيجة
        self.result = {
            'title': title,
            'description': description,
            'status': status,
            'priority': priority,
            'due_date': due_date
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """
        إلغاء العملية
        Cancel operation
        """
        self.dialog.destroy()
