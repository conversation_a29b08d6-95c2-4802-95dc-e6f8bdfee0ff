#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل برنامج إدارة المهام
Task Manager Launcher
"""

import sys
import os

# إضافة المجلد الحالي للمسار
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from main import main
    
    if __name__ == "__main__":
        print("🚀 بدء تشغيل برنامج إدارة المهام...")
        print("🚀 Starting Task Manager...")
        main()
        
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("❌ Import Error:", e)
    print("تأكد من وجود جميع الملفات المطلوبة")
    print("Make sure all required files exist")
    input("اضغط Enter للخروج...")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل البرنامج: {e}")
    print("❌ Error running application:", e)
    input("اضغط Enter للخروج...")
