# برنامج إدارة المهام - Task Manager

برنامج سطح مكتب لإدارة المهام مع واجهة مستخدم عربية، يدعم إضافة وتعديل وحذف والبحث في المهام.

## المميزات

- ✅ إضافة مهام جديدة مع تفاصيل كاملة
- ✅ تعديل المهام الموجودة
- ✅ حذف المهام
- ✅ البحث في المهام (العنوان والوصف)
- ✅ فلترة المهام حسب الحالة والأولوية
- ✅ تحديد حالة المهام (معلقة، قيد التنفيذ، مكتملة)
- ✅ تحديد أولوية المهام (عالية، متوسطة، منخفضة)
- ✅ تحديد تاريخ الاستحقاق
- ✅ واجهة مستخدم عربية سهلة الاستخدام
- ✅ حفظ البيانات في قاعدة بيانات SQLite

## متطلبات التشغيل

- Python 3.6 أو أحدث
- مكتبة tkinter (مدمجة مع Python)
- مكتبة tkcalendar (لاختيار التاريخ)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج

```bash
python main.py
```

## كيفية الاستخدام

### إضافة مهمة جديدة
1. انقر على زر "إضافة مهمة"
2. أدخل عنوان المهمة (مطلوب)
3. أدخل وصف المهمة (اختياري)
4. اختر الأولوية (عالية، متوسطة، منخفضة)
5. اختر تاريخ الاستحقاق (اختياري)
6. انقر "حفظ"

### تعديل مهمة
1. انقر نقرة مزدوجة على المهمة المراد تعديلها
2. أو اختر المهمة وانقر "تعديل"
3. عدل البيانات المطلوبة
4. انقر "حفظ"

### حذف مهمة
1. اختر المهمة المراد حذفها
2. انقر "حذف"
3. أكد الحذف

### البحث في المهام
- اكتب في مربع البحث للبحث في عناوين ووصف المهام
- البحث يتم تلقائياً أثناء الكتابة

### فلترة المهام
- استخدم قوائم الفلترة لعرض المهام حسب الحالة أو الأولوية
- اختر "الكل" لعرض جميع المهام

### تغيير حالة المهمة بسرعة
- انقر بالزر الأيمن على المهمة
- اختر "تحديد كمكتملة" أو "تحديد كقيد التنفيذ"

## بنية الملفات

```
├── main.py              # الملف الرئيسي للبرنامج
├── database.py          # وحدة قاعدة البيانات
├── task_dialog.py       # نافذة إضافة وتعديل المهام
├── requirements.txt     # متطلبات البرنامج
├── README.md           # ملف التوثيق
└── tasks.db            # قاعدة البيانات (يتم إنشاؤها تلقائياً)
```

## الدعم الفني

إذا واجهت أي مشاكل في التشغيل:

1. تأكد من تثبيت Python 3.6 أو أحدث
2. تأكد من تثبيت المتطلبات: `pip install -r requirements.txt`
3. تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

## التطوير المستقبلي

- إضافة فئات للمهام
- إضافة تذكيرات
- إضافة إحصائيات
- إضافة تصدير واستيراد البيانات
- إضافة ثيمات مختلفة
