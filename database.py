# -*- coding: utf-8 -*-
"""
وحدة قاعدة البيانات لبرنامج إدارة المهام
Database module for Task Management System
"""

import sqlite3
from datetime import datetime
import os

class TaskDatabase:
    def __init__(self, db_name="tasks.db"):
        """
        تهيئة قاعدة البيانات
        Initialize the database
        """
        self.db_name = db_name
        self.init_database()
    
    def init_database(self):
        """
        إنشاء جداول قاعدة البيانات
        Create database tables
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # إنشاء جدول المهام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                status TEXT DEFAULT 'pending',
                priority TEXT DEFAULT 'medium',
                created_date TEXT NOT NULL,
                due_date TEXT,
                completed_date TEXT
            )
        ''')
        
        # إنشاء جدول الفئات (اختياري للمستقبل)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                color TEXT DEFAULT '#007ACC'
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_task(self, title, description="", priority="medium", due_date=None):
        """
        إضافة مهمة جديدة
        Add a new task
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        created_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        cursor.execute('''
            INSERT INTO tasks (title, description, priority, created_date, due_date)
            VALUES (?, ?, ?, ?, ?)
        ''', (title, description, priority, created_date, due_date))
        
        task_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return task_id
    
    def get_all_tasks(self):
        """
        الحصول على جميع المهام
        Get all tasks
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, description, status, priority, created_date, due_date, completed_date
            FROM tasks
            ORDER BY created_date DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        return tasks
    
    def search_tasks(self, search_term):
        """
        البحث في المهام
        Search tasks
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, description, status, priority, created_date, due_date, completed_date
            FROM tasks
            WHERE title LIKE ? OR description LIKE ?
            ORDER BY created_date DESC
        ''', (f'%{search_term}%', f'%{search_term}%'))
        
        tasks = cursor.fetchall()
        conn.close()
        
        return tasks
    
    def update_task(self, task_id, title=None, description=None, status=None, priority=None, due_date=None):
        """
        تحديث مهمة
        Update a task
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # بناء استعلام التحديث ديناميكياً
        updates = []
        values = []
        
        if title is not None:
            updates.append("title = ?")
            values.append(title)
        
        if description is not None:
            updates.append("description = ?")
            values.append(description)
        
        if status is not None:
            updates.append("status = ?")
            values.append(status)
            
            # إذا تم تحديد الحالة كمكتملة، أضف تاريخ الإكمال
            if status == 'completed':
                updates.append("completed_date = ?")
                values.append(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        if priority is not None:
            updates.append("priority = ?")
            values.append(priority)
        
        if due_date is not None:
            updates.append("due_date = ?")
            values.append(due_date)
        
        if updates:
            values.append(task_id)
            query = f"UPDATE tasks SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, values)
            conn.commit()
        
        conn.close()
    
    def delete_task(self, task_id):
        """
        حذف مهمة
        Delete a task
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
        conn.commit()
        conn.close()
    
    def get_task_by_id(self, task_id):
        """
        الحصول على مهمة بالمعرف
        Get task by ID
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, description, status, priority, created_date, due_date, completed_date
            FROM tasks
            WHERE id = ?
        ''', (task_id,))
        
        task = cursor.fetchone()
        conn.close()
        
        return task
    
    def get_tasks_by_status(self, status):
        """
        الحصول على المهام حسب الحالة
        Get tasks by status
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, description, status, priority, created_date, due_date, completed_date
            FROM tasks
            WHERE status = ?
            ORDER BY created_date DESC
        ''', (status,))
        
        tasks = cursor.fetchall()
        conn.close()
        
        return tasks
    
    def get_tasks_by_priority(self, priority):
        """
        الحصول على المهام حسب الأولوية
        Get tasks by priority
        """
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, description, status, priority, created_date, due_date, completed_date
            FROM tasks
            WHERE priority = ?
            ORDER BY created_date DESC
        ''', (priority,))
        
        tasks = cursor.fetchall()
        conn.close()
        
        return tasks
