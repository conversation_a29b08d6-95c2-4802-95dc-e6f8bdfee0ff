#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لبرنامج إدارة المهام
Quick Start for Task Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime
import os

class QuickTaskManager:
    def __init__(self):
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("برنامج إدارة المهام - Task Manager")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحديث المهام
        self.load_tasks()
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.conn = sqlite3.connect("tasks_quick.db")
        cursor = self.conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                status TEXT DEFAULT 'pending',
                created_date TEXT NOT NULL
            )
        ''')
        self.conn.commit()
    
    def create_interface(self):
        """إنشاء الواجهة"""
        # العنوان
        title_label = tk.Label(self.root, text="📋 برنامج إدارة المهام", 
                              font=('Arial', 16, 'bold'), bg='#f0f0f0')
        title_label.pack(pady=10)
        
        # إطار الإدخال
        input_frame = tk.Frame(self.root, bg='#f0f0f0')
        input_frame.pack(pady=10, padx=20, fill='x')
        
        # عنوان المهمة
        tk.Label(input_frame, text="عنوان المهمة:", font=('Arial', 10, 'bold'), bg='#f0f0f0').pack(anchor='w')
        self.title_entry = tk.Entry(input_frame, font=('Arial', 10), width=50)
        self.title_entry.pack(fill='x', pady=(5, 10))
        
        # وصف المهمة
        tk.Label(input_frame, text="وصف المهمة:", font=('Arial', 10, 'bold'), bg='#f0f0f0').pack(anchor='w')
        self.desc_text = tk.Text(input_frame, height=3, font=('Arial', 10))
        self.desc_text.pack(fill='x', pady=(5, 10))
        
        # الأزرار
        button_frame = tk.Frame(input_frame, bg='#f0f0f0')
        button_frame.pack(fill='x')
        
        tk.Button(button_frame, text="➕ إضافة مهمة", command=self.add_task,
                 bg='#4CAF50', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        tk.Button(button_frame, text="✏️ تعديل", command=self.edit_task,
                 bg='#2196F3', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        tk.Button(button_frame, text="🗑️ حذف", command=self.delete_task,
                 bg='#f44336', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        tk.Button(button_frame, text="🔄 تحديث", command=self.load_tasks,
                 bg='#FF9800', fg='white', font=('Arial', 10, 'bold')).pack(side='left')
        
        # البحث
        search_frame = tk.Frame(self.root, bg='#f0f0f0')
        search_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(search_frame, text="🔍 البحث:", font=('Arial', 10, 'bold'), bg='#f0f0f0').pack(side='left')
        self.search_entry = tk.Entry(search_frame, font=('Arial', 10))
        self.search_entry.pack(side='left', fill='x', expand=True, padx=(10, 10))
        self.search_entry.bind('<KeyRelease>', self.search_tasks)
        
        tk.Button(search_frame, text="🔍 بحث", command=self.search_tasks,
                 bg='#9C27B0', fg='white', font=('Arial', 9)).pack(side='right')
        
        # قائمة المهام
        list_frame = tk.Frame(self.root, bg='#f0f0f0')
        list_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        tk.Label(list_frame, text="📝 قائمة المهام:", font=('Arial', 12, 'bold'), bg='#f0f0f0').pack(anchor='w')
        
        # إنشاء Listbox مع Scrollbar
        listbox_frame = tk.Frame(list_frame)
        listbox_frame.pack(fill='both', expand=True, pady=(5, 0))
        
        self.tasks_listbox = tk.Listbox(listbox_frame, font=('Arial', 10), height=15)
        scrollbar = tk.Scrollbar(listbox_frame, orient='vertical', command=self.tasks_listbox.yview)
        self.tasks_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.tasks_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج
        self.tasks_listbox.bind('<Double-1>', lambda e: self.edit_task())
        
        # شريط الحالة
        self.status_label = tk.Label(self.root, text="جاهز", relief='sunken', anchor='w', bg='#e0e0e0')
        self.status_label.pack(side='bottom', fill='x')
    
    def add_task(self):
        """إضافة مهمة جديدة"""
        title = self.title_entry.get().strip()
        description = self.desc_text.get('1.0', tk.END).strip()
        
        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان المهمة")
            return
        
        try:
            cursor = self.conn.cursor()
            created_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute('''
                INSERT INTO tasks (title, description, created_date)
                VALUES (?, ?, ?)
            ''', (title, description, created_date))
            
            self.conn.commit()
            
            # مسح الحقول
            self.title_entry.delete(0, tk.END)
            self.desc_text.delete('1.0', tk.END)
            
            # تحديث القائمة
            self.load_tasks()
            
            self.status_label.config(text=f"✅ تم إضافة المهمة: {title}")
            messagebox.showinfo("نجح", "تم إضافة المهمة بنجاح!")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
    
    def load_tasks(self):
        """تحميل المهام"""
        self.tasks_listbox.delete(0, tk.END)
        
        cursor = self.conn.cursor()
        cursor.execute("SELECT id, title, description, status, created_date FROM tasks ORDER BY created_date DESC")
        tasks = cursor.fetchall()
        
        for task in tasks:
            status_icon = "✅" if task[3] == "completed" else "⏳" if task[3] == "in_progress" else "📋"
            display_text = f"{status_icon} {task[1]}"
            if task[2]:
                display_text += f" - {task[2][:30]}..."
            
            self.tasks_listbox.insert(tk.END, display_text)
            # حفظ ID المهمة كبيانات مخفية
            self.tasks_listbox.insert(tk.END, f"ID:{task[0]}")
            self.tasks_listbox.delete(tk.END)  # حذف السطر المؤقت
            
        self.status_label.config(text=f"📊 إجمالي المهام: {len(tasks)}")
    
    def edit_task(self):
        """تعديل المهمة المحددة"""
        selection = self.tasks_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للتعديل")
            return
        
        # الحصول على المهمة من قاعدة البيانات
        # هذا تبسيط - في التطبيق الكامل نحتاج لحفظ IDs
        messagebox.showinfo("قريباً", "ميزة التعديل ستكون متاحة في النسخة الكاملة")
    
    def delete_task(self):
        """حذف المهمة المحددة"""
        selection = self.tasks_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذه المهمة؟"):
            # هذا تبسيط - في التطبيق الكامل نحتاج لحفظ IDs
            self.tasks_listbox.delete(selection[0])
            self.status_label.config(text="🗑️ تم حذف المهمة")
    
    def search_tasks(self, event=None):
        """البحث في المهام"""
        search_term = self.search_entry.get().strip()
        
        if not search_term:
            self.load_tasks()
            return
        
        self.tasks_listbox.delete(0, tk.END)
        
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, title, description, status, created_date 
            FROM tasks 
            WHERE title LIKE ? OR description LIKE ?
            ORDER BY created_date DESC
        ''', (f'%{search_term}%', f'%{search_term}%'))
        
        tasks = cursor.fetchall()
        
        for task in tasks:
            status_icon = "✅" if task[3] == "completed" else "⏳" if task[3] == "in_progress" else "📋"
            display_text = f"{status_icon} {task[1]}"
            if task[2]:
                display_text += f" - {task[2][:30]}..."
            
            self.tasks_listbox.insert(tk.END, display_text)
        
        self.status_label.config(text=f"🔍 نتائج البحث: {len(tasks)}")
    
    def run(self):
        """تشغيل البرنامج"""
        # إضافة بعض المهام التجريبية
        self.add_sample_tasks()
        
        print("🚀 تم تشغيل برنامج إدارة المهام")
        print("🚀 Task Manager is running")
        
        # تشغيل الحلقة الرئيسية
        self.root.mainloop()
        
        # إغلاق قاعدة البيانات
        self.conn.close()
    
    def add_sample_tasks(self):
        """إضافة مهام تجريبية"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM tasks")
        count = cursor.fetchone()[0]
        
        if count == 0:  # إضافة مهام تجريبية فقط إذا كانت قاعدة البيانات فارغة
            sample_tasks = [
                ("مراجعة التقارير الشهرية", "مراجعة وتحليل التقارير المالية للشهر الماضي"),
                ("اجتماع الفريق", "اجتماع أسبوعي مع فريق العمل لمناقشة التقدم"),
                ("تطوير الموقع الإلكتروني", "إنهاء تطوير الصفحة الرئيسية للموقع"),
                ("تحضير العرض التقديمي", "إعداد عرض تقديمي للعميل الجديد")
            ]
            
            created_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            for title, desc in sample_tasks:
                cursor.execute('''
                    INSERT INTO tasks (title, description, created_date)
                    VALUES (?, ?, ?)
                ''', (title, desc, created_date))
            
            self.conn.commit()


def main():
    """الدالة الرئيسية"""
    try:
        print("=" * 50)
        print("🎯 مرحباً بك في برنامج إدارة المهام")
        print("🎯 Welcome to Task Manager")
        print("=" * 50)
        
        # إنشاء وتشغيل التطبيق
        app = QuickTaskManager()
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")


if __name__ == "__main__":
    main()
